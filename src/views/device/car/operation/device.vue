<template>
    <div class="custom-table flex flex-col">
        <!-- 列设置按钮 -->
        <div class="table-header flex justify-end gap-x-3">
            <div class="flex items-center">
                <el-input
                    v-model="searchValueD"
                    width="200"
                    :placeholder="$t('InputToSearch01')"
                >
                    <template #append>
                        <el-button
                            plain
                            :icon="Search"
                            @click="debouncedOnSearchD"
                        />
                    </template>
                </el-input>
            </div>
            <el-button @click="showColumnSelector" plain round>
                {{ $t('Custom Columns') }}
                <iconSvg name="customList" :class="'w-4 h-4 ml-1'" />
            </el-button>
            <el-button @click="openFilter" plain round>
                <span>{{ $t('Device Filter') }}</span>
                <iconSvg name="filter" :class="'w-4 h-4 ml-1'" />
            </el-button>
            <el-button
                @click="openBatchBindDrawer"
                plain
                round
                v-if="isOperator"
            >
                {{ $t('station_bangdingqiye') }}
                <iconSvg name="bind" :class="'w-4 h-4 ml-1'" />
            </el-button>
            <el-button @click="addDevice" plain round v-if="isOperator">
                {{ $t('Devices add') }}
                <iconSvg name="addDevice" :class="'w-4 h-4 ml-1'" />
            </el-button>
            <el-dropdown plain round @click="addDevice" placement="bottom-end">
                <el-button plain round>
                    <span class="">{{ $t('Batch Operations') }}</span>
                    <iconSvg name="batch" :class="'w-4 h-4 ml-1'" />
                </el-button>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item @click="addDeviceImport">
                            <div class="flex justify-end items-center">
                                <span>{{ $t('add_device') }}</span>
                                <iconSvg
                                    name="addDevice"
                                    :class="'w-4 h-4 ml-1'"
                                />
                            </div>
                        </el-dropdown-item>
                        <el-dropdown-item @click="batchRenewal">
                            <div class="flex justify-end items-center">
                                <span>{{ $t('Service Renewal') }}</span>
                                <iconSvg
                                    name="renewal"
                                    :class="'w-4 h-4 ml-1'"
                                />
                            </div>
                        </el-dropdown-item>

                        <el-dropdown-item @click="BatchDownloadQRCode">
                            <div class="flex justify-end items-center">
                                <span>{{ $t('Download QR Code') }}</span>
                                <iconSvg
                                    name="qrcode"
                                    :class="'w-4 h-4 ml-1'"
                                />
                            </div>
                        </el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
        </div>
        <div
            class="flex-1 h-0 overflow-y-auto mt-5 table-box"
            v-loading="tableLoading"
        >
            <!-- 表格主体 -->
            <div class="w-full h-full">
                <el-table
                    :data="tableData"
                    :height="'100%'"
                    @row-click="rowClick"
                    @sort-change="handleSortChange"
                    style="width: 100%"
                    :empty-text="$t('zanwushuju')"
                >
                    <!-- 动态渲染列 -->
                    <template v-for="col in visibleColumns" :key="col.key">
                        <!-- 设备编号列 -->
                        <el-table-column
                            v-if="col.key === 'sn'"
                            prop="sn"
                            :label="$t('Device No')"
                            width="280"
                            fixed="left"
                        >
                            <template #default="{ row }">
                                <div class="flex flex-col">
                                    <div class="text-100">{{ row.sn }}</div>
                                    <div class="text-80">
                                        {{ $t('BatteryNo') }}：{{
                                            row.batteryNo || '-'
                                        }}
                                    </div>
                                </div>
                            </template>
                        </el-table-column>

                        <!-- HWID列 -->
                        <el-table-column
                            v-else-if="col.key === 'hwid'"
                            prop="hwid"
                            label="HWID"
                            width="180"
                            :align="col.align"
                        >
                            <template #default="{ row }">
                                <div class="flex flex-col break-all">
                                    {{ row.hwid }}
                                </div>
                            </template>
                        </el-table-column>

                        <!-- IMEI列 -->
                        <el-table-column
                            v-else-if="col.key === 'imei'"
                            prop="imei"
                            label="IMEI"
                            width="156"
                            :align="col.align"
                        />

                        <!-- 车辆类型列 -->
                        <el-table-column
                            v-else-if="col.key === 'vehicleType'"
                            prop="vehicleType"
                            :label="$t('Vehicle type')"
                            :width="locale == 'en' ? '144' : '124'"
                            :align="col.align"
                        >
                            <template #header>
                                <div class="flex items-center">
                                    <span class="mr-2 text-sm">{{
                                        $t('Vehicle type')
                                    }}</span>
                                    <el-popover
                                        trigger="click"
                                        width="200"
                                        v-model:visible="
                                            vehicleTypeFilterVisible
                                        "
                                    >
                                        <template #reference>
                                            <div
                                                class="cursor-pointer leading-4 filter-column"
                                                :class="
                                                    vehicleTypeFilter?.length
                                                        ? 'hasFilter'
                                                        : ''
                                                "
                                                style="margin-top: 3px"
                                                data-vehicle-type-filter
                                                tabindex="0"
                                                role="button"
                                                @keydown.enter="
                                                    vehicleTypeFilterVisible =
                                                        !vehicleTypeFilterVisible
                                                "
                                                @keydown.space.prevent="
                                                    vehicleTypeFilterVisible =
                                                        !vehicleTypeFilterVisible
                                                "
                                            >
                                                <iconSvg
                                                    name="filter"
                                                    class="w-3.5 h-3.5"
                                                />
                                            </div>
                                        </template>
                                        <div class="filter-wrapper">
                                            <div class="filter-options">
                                                <div class="overflow-y-auto">
                                                    <el-checkbox
                                                        v-for="item in vehicleTypes"
                                                        :key="item.value"
                                                        :model-value="
                                                            selectedVehicleTypes.includes(
                                                                item.value
                                                            )
                                                        "
                                                        @change="
                                                            (val) =>
                                                                onVehicleTypeFilterChange(
                                                                    item.value,
                                                                    val
                                                                )
                                                        "
                                                    >
                                                        {{ item.label }}
                                                    </el-checkbox>
                                                </div>
                                            </div>
                                            <div
                                                class="flex justify-end gap-2 pt-2 border-t border-border dark:border-border-dark-008"
                                            >
                                                <el-button
                                                    round
                                                    @click="
                                                        onVehicleTypeFilterReset
                                                    "
                                                    >{{
                                                        $t('reset')
                                                    }}</el-button
                                                >
                                                <el-button
                                                    type="primary"
                                                    plain
                                                    round
                                                    @click="
                                                        onVehicleTypeFilterConfirm
                                                    "
                                                    >{{
                                                        $t('Confirm')
                                                    }}</el-button
                                                >
                                            </div>
                                        </div>
                                    </el-popover>
                                </div>
                            </template>
                            <template #default="{ row }">
                                {{ getVehicleType(row.vehicleType) }}
                            </template>
                        </el-table-column>

                        <!-- 设备型号列 -->
                        <el-table-column
                            v-else-if="col.key === 'model'"
                            prop="model"
                            :label="$t('Device model')"
                            width="164"
                            :align="col.align"
                        >
                            <template #header>
                                <div class="flex items-center">
                                    <span class="mr-2 text-sm">{{
                                        $t('Device model')
                                    }}</span>
                                    <el-popover
                                        trigger="click"
                                        width="200"
                                        v-model:visible="modelFilterVisible"
                                    >
                                        <template #reference>
                                            <div
                                                class="cursor-pointer leading-4 filter-column"
                                                :class="
                                                    modelFilter?.length
                                                        ? 'hasFilter'
                                                        : ''
                                                "
                                                style="margin-top: 3px"
                                                data-model-filter
                                                tabindex="0"
                                                role="button"
                                                @keydown.enter="
                                                    modelFilterVisible =
                                                        !modelFilterVisible
                                                "
                                                @keydown.space.prevent="
                                                    modelFilterVisible =
                                                        !modelFilterVisible
                                                "
                                            >
                                                <iconSvg
                                                    name="filter"
                                                    class="w-3.5 h-3.5"
                                                />
                                            </div>
                                        </template>
                                        <div class="filter-wrapper">
                                            <div class="filter-options">
                                                <div class="overflow-y-auto">
                                                    <el-checkbox
                                                        v-for="item in deviceOptions"
                                                        :key="item.value"
                                                        :model-value="
                                                            selectedModels.includes(
                                                                item.value
                                                            )
                                                        "
                                                        @change="
                                                            (val) =>
                                                                onModelFilterChange(
                                                                    item.value,
                                                                    val
                                                                )
                                                        "
                                                    >
                                                        {{ item.label }}
                                                    </el-checkbox>
                                                </div>
                                            </div>
                                            <div
                                                class="flex justify-end gap-2 pt-2 border-t border-border dark:border-border-dark-008"
                                            >
                                                <el-button
                                                    round
                                                    @click="onModelFilterReset"
                                                    >{{
                                                        $t('reset')
                                                    }}</el-button
                                                >
                                                <el-button
                                                    type="primary"
                                                    plain
                                                    round
                                                    @click="
                                                        onModelFilterConfirm
                                                    "
                                                    >{{
                                                        $t('Confirm')
                                                    }}</el-button
                                                >
                                            </div>
                                        </div>
                                    </el-popover>
                                </div>
                            </template>
                            <template #default="{ row }">
                                {{ row.model || '-' }}
                            </template>
                        </el-table-column>

                        <!-- 激活状态列 -->
                        <el-table-column
                            v-else-if="col.key === 'activeStatus'"
                            prop="activeStatus"
                            :label="$t('activation_status')"
                            :width="locale == 'en' ? '164' : '144'"
                            :align="col.align"
                        >
                            <template #header>
                                <div class="flex items-center">
                                    <span class="mr-2 text-sm">{{
                                        $t('activation_status')
                                    }}</span>
                                    <el-popover
                                        trigger="click"
                                        width="200"
                                        v-model:visible="
                                            activeStatusFilterVisible
                                        "
                                    >
                                        <template #reference>
                                            <div
                                                class="cursor-pointer leading-4 filter-column"
                                                :class="
                                                    activeStatusFilter?.length
                                                        ? 'hasFilter'
                                                        : ''
                                                "
                                                style="margin-top: 3px"
                                                data-active-status-filter
                                                tabindex="0"
                                                role="button"
                                                @keydown.enter="
                                                    activeStatusFilterVisible =
                                                        !activeStatusFilterVisible
                                                "
                                                @keydown.space.prevent="
                                                    activeStatusFilterVisible =
                                                        !activeStatusFilterVisible
                                                "
                                            >
                                                <iconSvg
                                                    name="filter"
                                                    class="w-3.5 h-3.5"
                                                />
                                            </div>
                                        </template>
                                        <div class="filter-wrapper">
                                            <div class="filter-options">
                                                <div class="overflow-y-auto">
                                                    <el-radio-group
                                                        v-model="
                                                            selectedActiveStatus
                                                        "
                                                    >
                                                        <el-radio :label="1">{{
                                                            $t('Activated')
                                                        }}</el-radio>
                                                        <el-radio :label="0">{{
                                                            $t('NotActivated')
                                                        }}</el-radio>
                                                    </el-radio-group>
                                                </div>
                                            </div>
                                            <div
                                                class="flex justify-end gap-2 pt-2 border-t border-border dark:border-border-dark-008"
                                            >
                                                <el-button
                                                    round
                                                    @click="
                                                        onActiveStatusFilterReset
                                                    "
                                                    >{{
                                                        $t('reset')
                                                    }}</el-button
                                                >
                                                <el-button
                                                    type="primary"
                                                    plain
                                                    round
                                                    @click="
                                                        onActiveStatusFilterConfirm
                                                    "
                                                    >{{
                                                        $t('Confirm')
                                                    }}</el-button
                                                >
                                            </div>
                                        </div>
                                    </el-popover>
                                </div>
                            </template>
                            <template #default="{ row }">
                                {{
                                    $t(formatterActiveStatus(row.activeStatus))
                                }}
                            </template>
                        </el-table-column>

                        <!-- 系统状态列 -->
                        <el-table-column
                            v-else-if="col.key === 'status'"
                            prop="status"
                            :label="$t('system_status')"
                            width="144"
                            :align="col.align"
                        >
                            <template #header>
                                <div class="flex items-center">
                                    <span class="mr-2 text-sm">{{
                                        $t('system_status')
                                    }}</span>
                                    <el-popover
                                        trigger="click"
                                        width="200"
                                        v-model:visible="statusFilterVisible"
                                    >
                                        <template #reference>
                                            <div
                                                class="cursor-pointer leading-4 filter-column"
                                                :class="
                                                    statusFilter?.length
                                                        ? 'hasFilter'
                                                        : ''
                                                "
                                                style="margin-top: 3px"
                                                data-status-filter
                                                tabindex="0"
                                                role="button"
                                                @keydown.enter="
                                                    statusFilterVisible =
                                                        !statusFilterVisible
                                                "
                                                @keydown.space.prevent="
                                                    statusFilterVisible =
                                                        !statusFilterVisible
                                                "
                                            >
                                                <iconSvg
                                                    name="filter"
                                                    class="w-3.5 h-3.5"
                                                />
                                            </div>
                                        </template>
                                        <div class="filter-wrapper">
                                            <div class="filter-options">
                                                <div class="overflow-y-auto">
                                                    <el-checkbox
                                                        v-for="item in chargeStateP"
                                                        :key="item.value"
                                                        :model-value="
                                                            selectedStatus.includes(
                                                                item.value
                                                            )
                                                        "
                                                        @change="
                                                            (val) =>
                                                                onStatusFilterChange(
                                                                    item.value,
                                                                    val
                                                                )
                                                        "
                                                    >
                                                        {{ $t(item.label) }}
                                                    </el-checkbox>
                                                </div>
                                            </div>
                                            <div
                                                class="flex justify-end gap-2 pt-2 border-t border-border dark:border-border-dark-008"
                                            >
                                                <el-button
                                                    round
                                                    @click="onStatusFilterReset"
                                                    >{{
                                                        $t('reset')
                                                    }}</el-button
                                                >
                                                <el-button
                                                    type="primary"
                                                    plain
                                                    round
                                                    @click="
                                                        onStatusFilterConfirm
                                                    "
                                                    >{{
                                                        $t('Confirm')
                                                    }}</el-button
                                                >
                                            </div>
                                        </div>
                                    </el-popover>
                                </div>
                            </template>
                            <template #default="{ row }">
                                {{ formatterStatus(row) }}
                            </template>
                        </el-table-column>

                        <!-- 服务状态列 -->
                        <el-table-column
                            v-else-if="col.key === 'serviceStatus'"
                            prop="serviceStatus"
                            :label="$t('service_status')"
                            width="144"
                            :align="col.align"
                        >
                            <template #header>
                                <div class="flex items-center">
                                    <span class="mr-2 text-sm">{{
                                        $t('service_status')
                                    }}</span>
                                    <el-popover
                                        trigger="click"
                                        width="200"
                                        v-model:visible="
                                            serviceStatusFilterVisible
                                        "
                                    >
                                        <template #reference>
                                            <div
                                                class="cursor-pointer leading-4 filter-column"
                                                :class="
                                                    serviceStatusFilter?.length
                                                        ? 'hasFilter'
                                                        : ''
                                                "
                                                style="margin-top: 3px"
                                                data-service-status-filter
                                                tabindex="0"
                                                role="button"
                                                @keydown.enter="
                                                    serviceStatusFilterVisible =
                                                        !serviceStatusFilterVisible
                                                "
                                                @keydown.space.prevent="
                                                    serviceStatusFilterVisible =
                                                        !serviceStatusFilterVisible
                                                "
                                            >
                                                <iconSvg
                                                    name="filter"
                                                    class="w-3.5 h-3.5"
                                                />
                                            </div>
                                        </template>
                                        <div class="filter-wrapper">
                                            <div class="filter-options">
                                                <div class="overflow-y-auto">
                                                    <el-radio-group
                                                        v-model="
                                                            selectedServiceStatus
                                                        "
                                                    >
                                                        <el-radio :label="1">{{
                                                            $t('Normal')
                                                        }}</el-radio>
                                                        <el-radio :label="2">{{
                                                            $t('Expired')
                                                        }}</el-radio>
                                                    </el-radio-group>
                                                </div>
                                            </div>
                                            <div
                                                class="flex justify-end gap-2 pt-2 border-t border-border dark:border-border-dark-008"
                                            >
                                                <el-button
                                                    round
                                                    @click="
                                                        onServiceStatusFilterReset
                                                    "
                                                    >{{
                                                        $t('reset')
                                                    }}</el-button
                                                >
                                                <el-button
                                                    type="primary"
                                                    plain
                                                    round
                                                    @click="
                                                        onServiceStatusFilterConfirm
                                                    "
                                                    >{{
                                                        $t('Confirm')
                                                    }}</el-button
                                                >
                                            </div>
                                        </div>
                                    </el-popover>
                                </div>
                            </template>
                            <template #default="{ row }">
                                {{ formatterServiceStatus(row.serviceStatus) }}
                            </template>
                        </el-table-column>

                        <!-- 服务到期时间列 -->
                        <el-table-column
                            v-else-if="col.key === 'serviceExpireDate'"
                            prop="serviceExpireDate"
                            :label="$t('service_expire_time')"
                            :width="locale == 'en' ? '164' : '144'"
                            sortable="custom"
                            :sort-orders="['ascending', 'descending']"
                            :align="col.align"
                        />

                        <!-- 激活日期列 -->
                        <el-table-column
                            v-else-if="col.key === 'activeTime'"
                            prop="activeTime"
                            :label="$t('activation_date')"
                            :width="locale == 'en' ? '164' : '144'"
                            sortable="custom"
                            :sort-orders="['ascending', 'descending']"
                            :align="col.align"
                        >
                            <template #default="{ row }">
                                {{ formatterActiveTime(row) }}
                            </template>
                        </el-table-column>

                        <!-- 信号强度列 -->
                        <el-table-column
                            v-else-if="col.key === 'signal4g'"
                            prop="signal4g"
                            :label="$t('signal_strength')"
                            :width="locale == 'en' ? '164' : '120'"
                            sortable="custom"
                            :sort-orders="['ascending', 'descending']"
                            :align="col.align"
                        />

                        <!-- 最后数据时间列 -->
                        <el-table-column
                            v-else-if="col.key === 'lastHeartbeatTime'"
                            prop="lastHeartbeatTime"
                            :label="$t('last_data_time')"
                            width="144"
                            sortable="custom"
                            :sort-orders="['ascending', 'descending']"
                            :align="col.align"
                        />

                        <!-- SOC列 -->
                        <el-table-column
                            v-else-if="col.key === 'soc'"
                            prop="soc"
                            label="SOC"
                            width="80"
                            sortable="custom"
                            :sort-orders="['ascending', 'descending']"
                            :align="col.align"
                        />

                        <!-- SOH列 -->
                        <el-table-column
                            v-else-if="col.key === 'soh'"
                            prop="soh"
                            label="SOH"
                            width="80"
                            sortable="custom"
                            :sort-orders="['ascending', 'descending']"
                            :align="col.align"
                        />

                        <!-- 告警数列 -->
                        <el-table-column
                            v-else-if="col.key === 'alarmNub'"
                            prop="alarmNub"
                            :label="$t('alarm_count')"
                            :width="locale == 'en' ? '140' : '124'"
                            sortable="custom"
                            :sort-orders="['ascending', 'descending']"
                            :align="col.align"
                        />

                        <!-- 总充电时长列 -->
                        <el-table-column
                            v-else-if="col.key === 'chgTimeSum'"
                            prop="chgTimeSum"
                            :label="$t('total_charge_time')"
                            :width="locale == 'en' ? '188' : '148'"
                            sortable="custom"
                            :sort-orders="['ascending', 'descending']"
                            :align="col.align"
                        />

                        <!-- 总充电容量列 -->
                        <el-table-column
                            v-else-if="col.key === 'chgCapSum'"
                            prop="chgCapSum"
                            :label="$t('total_charge_capacity')"
                            :width="locale == 'en' ? '220' : '148'"
                            sortable="custom"
                            :sort-orders="['ascending', 'descending']"
                            :align="col.align"
                        />

                        <!-- 总放电时长列 -->
                        <el-table-column
                            v-else-if="col.key === 'dsgTimeSum'"
                            prop="dsgTimeSum"
                            :label="$t('total_discharge_time')"
                            :width="locale == 'en' ? '220' : '148'"
                            sortable="custom"
                            :sort-orders="['ascending', 'descending']"
                            :align="col.align"
                        />

                        <!-- 总放电容量列 -->
                        <el-table-column
                            v-else-if="col.key === 'dsgCapSum'"
                            prop="dsgCapSum"
                            :label="$t('total_discharge_capacity')"
                            :width="locale == 'en' ? '240' : '148'"
                            sortable="custom"
                            :sort-orders="['ascending', 'descending']"
                            :align="col.align"
                        />

                        <!-- 最后充电时间列 -->
                        <el-table-column
                            v-else-if="col.key === 'lastChargeTime'"
                            prop="lastChargeTime"
                            :label="$t('last_charge_time')"
                            :width="locale == 'en' ? '188' : '148'"
                            sortable="custom"
                            :sort-orders="['ascending', 'descending']"
                            :align="col.align"
                        />

                        <!-- 最后放电时间列 -->
                        <el-table-column
                            v-else-if="col.key === 'lastDischargeTime'"
                            prop="lastDischargeTime"
                            :label="$t('last_discharge_time')"
                            :width="locale == 'en' ? '188' : '144'"
                            sortable="custom"
                            :sort-orders="['ascending', 'descending']"
                            :align="col.align"
                        />

                        <!-- 循环次数列 -->
                        <el-table-column
                            v-else-if="col.key === 'cycleCount'"
                            prop="cycleCount"
                            :label="$t('Cycle times')"
                            width="144"
                            sortable="custom"
                            :sort-orders="['ascending', 'descending']"
                            :align="col.align"
                        />

                        <!-- 添加站点列 -->
                        <el-table-column
                            v-else-if="col.key === 'customerName'"
                            prop="customerName"
                            :label="$t('station_tianjiazhandian')"
                            width="188"
                            :align="col.align"
                        />

                        <!-- 操作列 -->
                        <el-table-column
                            v-else-if="col.key === 'Operation'"
                            :label="$t('Operation')"
                            :width="locale == 'en' ? '100' : '88'"
                            align="center"
                            fixed="right"
                        >
                            <template #default="{ row }">
                                <div
                                    class="flex items-center justify-center mx-auto"
                                >
                                    <el-dropdown
                                        trigger="hover"
                                        @command="
                                            (command) =>
                                                handleOperation(command, row)
                                        "
                                        class="operation-drop"
                                    >
                                        <div
                                            class="cursor-pointer outline-none border-none mx-auto w-10 h-10 leading-10 text-center operation-icon"
                                            style="margin-top: 3px"
                                            @click.stop
                                        >
                                            <iconSvg
                                                name="arrowdrop"
                                                class="w-3.5 h-3.5 text-80 dark:text-80-dark"
                                            />
                                        </div>
                                        <template #dropdown>
                                            <el-dropdown-menu>
                                                <el-dropdown-item
                                                    command="bindDevice"
                                                >
                                                    <span>{{
                                                        $t(
                                                            'station_bangdingqiye'
                                                        )
                                                    }}</span>
                                                    <iconSvg
                                                        name="bind"
                                                        :class="'w-4 h-4 ml-1'"
                                                    />
                                                </el-dropdown-item>

                                                <el-dropdown-item
                                                    command="serviceRenewal"
                                                >
                                                    <span>{{
                                                        $t('Service Renewal')
                                                    }}</span>
                                                    <iconSvg
                                                        name="renewal"
                                                        :class="'w-4 h-4 ml-1'"
                                                    />
                                                </el-dropdown-item>
                                                <el-dropdown-item
                                                    command="deleteDevice"
                                                    :disabled="
                                                        !!row.customerId ||
                                                        row.activeStatus != 0
                                                    "
                                                >
                                                    <span>{{
                                                        $t('Delete Device')
                                                    }}</span>
                                                    <iconSvg
                                                        name="close1"
                                                        :class="'w-4 h-4 ml-1'"
                                                    />
                                                </el-dropdown-item>
                                                <el-dropdown-item
                                                    command="viewQRCode"
                                                >
                                                    <span>{{
                                                        $t('Look QR Code')
                                                    }}</span>
                                                    <iconSvg
                                                        name="qrcode"
                                                        :class="'w-4 h-4 ml-1'"
                                                    />
                                                </el-dropdown-item>
                                            </el-dropdown-menu>
                                        </template>
                                    </el-dropdown>
                                </div>
                            </template>
                        </el-table-column>
                    </template>
                </el-table>
            </div>
        </div>
        <div class="flex justify-center mt-4">
            <el-pagination
                background
                layout="prev, pager, next"
                :total="devicePageTotal"
                v-model:current-page="devicePageInfo.current"
                :page-size="devicePageInfo.size"
                @change="debouncedDevicePageChange"
                @size-change="handleDeviceSizeChange"
            />
        </div>

        <!-- 列选择器弹窗 -->
        <!-- 列选择器弹窗 -->
        <el-drawer
            v-model="dialogVisible"
            :size="500"
            :lockScroll="true"
            :show-close="false"
        >
            <template #header>
                <div
                    class="drawer-header flex items-center justify-between leading-5.5"
                >
                    <div class="drawer-header text-title dark:text-title-dark">
                        <span>{{ $t('column_settings') }}</span>
                    </div>
                    <div class="flex gap-x-3">
                        <el-button plain round @click="closeDrawer">{{
                            $t('common_guanbi')
                        }}</el-button>
                        <el-button plain round @click="onSave" type="primary">{{
                            $t('Confirm')
                        }}</el-button>
                    </div>
                </div>
            </template>
            <div class="flex items-start h-full">
                <div class="flex-1">
                    <div class="flex justify-start items-center">
                        <el-checkbox
                            v-model="checkAll"
                            :indeterminate="isIndeterminate"
                            @change="handleCheckAllChange"
                            style="width: auto"
                        >
                            {{ $t('Select all')
                            }}{{
                                checkedColumns.length
                                    ? '(' + (checkedColumns.length - 1) + ')'
                                    : ''
                            }}
                        </el-checkbox>
                        <!-- <el-button
                            type="text"
                            size="small"
                            @click="resetToDefault"
                            class="ml-4"
                        >
                            {{ $t('reset') }}
                        </el-button> -->
                        <!-- <div
                            class="ml-4 text-title dark:text-title-dark cursor-pointer select-none"
                            @click="onInvert"
                        >
                            反选
                        </div> -->
                    </div>
                    <el-checkbox-group
                        v-model="checkedColumns"
                        @change="onChangeCheckedColumns"
                        class="column-selector"
                    >
                        <el-checkbox
                            v-for="(item, index) in columnList"
                            :value="item.key"
                            :key="item.key"
                            :disabled="index < 2 || item.key === 'Operation'"
                            class="pl-4 relative"
                            v-show="item.key !== 'Operation'"
                        >
                            <div
                                class="absolute w-2.5 h-2.5 border-l border-b left-0 rounded-bl-sm opacity-60"
                            ></div>
                            {{ item.title }}
                        </el-checkbox>
                    </el-checkbox-group>
                </div>
                <el-divider direction="vertical" style="height: 100%" />
                <div class="flex-1 column-draggable">
                    <draggable
                        v-model="draggabledColumns"
                        item-key="key"
                        :disabled="false"
                        handle=".drag-handle"
                        @end="handleDragEnd"
                        :move="checkMove"
                    >
                        <template #item="{ element, index }">
                            <div
                                class="column-item"
                                v-show="element.key !== 'Operation'"
                            >
                                <el-icon
                                    class="drag-handle-disabled"
                                    v-if="index < 2"
                                >
                                    <iconSvg name="lock" class="w-5 h-5" />
                                </el-icon>
                                <el-icon class="drag-handle" v-else>
                                    <iconSvg name="drag" class="w-5 h-5" />
                                </el-icon>
                                <div
                                    class="text-title dark:text-title-dark flex-1"
                                    :class="
                                        index < 2
                                            ? ' opacity-40 select-none cursor-not-allowed'
                                            : ''
                                    "
                                >
                                    {{ element.title }}
                                </div>
                                <div
                                    class="w-6 h-6 flex items-center justify-center text-title dark:text-title-dark"
                                    :class="
                                        index < 2
                                            ? ' opacity-60 cursor-not-allowed'
                                            : 'cursor-pointer'
                                    "
                                    @click="onDeleteItem(element)"
                                >
                                    <el-icon size="14">
                                        <CloseBold />
                                    </el-icon>
                                </div>
                            </div>
                        </template>
                    </draggable>
                </div>
            </div>
        </el-drawer>
        <el-drawer
            v-model="dialogFormVisible"
            :size="486"
            :show-close="false"
            @close="cancelAdd"
            wrapClassName="drawerBox"
        >
            <template #header>
                <div
                    class="drawer-header flex items-center justify-between leading-5.5"
                >
                    <div
                        class="drawer-header text-primary-text dark:text-80-dark"
                    >
                        <span>{{ $t('add_device') }}</span>
                    </div>
                    <div class="flex gap-x-3 items-center">
                        <el-button plain round @click="cancelAdd">{{
                            $t('Cancle')
                        }}</el-button>
                        <el-button
                            plain
                            type="primary"
                            round
                            @click="confirmAdd()"
                            >{{ $t('Confirm') }}</el-button
                        >
                    </div>
                </div>
            </template>
            <div class="forms">
                <el-form
                    ref="formRef"
                    :model="formData"
                    :label-width="itemLabelWidth"
                    label-position="left"
                >
                    <el-form-item
                        :label="$t('Belonging project')"
                        prop="projectNo"
                    >
                        <el-select
                            v-model="formData.projectNo"
                            :placeholder="$t('Belonging project')"
                            class="w-full"
                        >
                            <el-option
                                v-for="item in projectData"
                                :key="item.projectNo"
                                :label="item.projectName"
                                :value="item.projectNo"
                            />
                        </el-select>
                    </el-form-item>
                    <div class="mb-2 text-primary-text dark:text-80-dark">
                        {{ $t('Device info') }}
                    </div>
                    <!-- 电池列表 -->
                    <div
                        v-for="(item, index) in formData.batteryList"
                        :key="index"
                        class="bg-background dark:bg-ffffff-dark px-3 py-4 mb-3 rounded"
                    >
                        <div class="flex items-center justify-between mb-1">
                            <div
                                class="font-medium text-title dark:text-title-dark"
                            >
                                {{ $t('Vehicles') }}{{ index + 1 }}
                            </div>
                        </div>

                        <el-form-item
                            :label="$t('Device No')"
                            :prop="`batteryList.${index}.sn`"
                        >
                            <el-input
                                v-model="item.sn"
                                :placeholder="$t('Device No')"
                            />
                        </el-form-item>
                        <el-form-item
                            :label="$t('BatteryNo')"
                            :prop="`batteryList.${index}.batteryNo`"
                        >
                            <el-input
                                v-model="item.batteryNo"
                                :placeholder="$t('BatteryNo')"
                            />
                        </el-form-item>

                        <div
                            class="flex justify-center items-center gap-x-3 text-xs"
                        >
                            <el-button
                                @click="copyBatteryRow(item, index)"
                                plain
                                round
                                size="small"
                                type="primary"
                            >
                                {{ $t('Copy') }}
                            </el-button>
                            <el-button
                                v-if="formData.batteryList.length > 1"
                                @click="removeBatteryRow(index)"
                                round
                                size="small"
                                linear
                            >
                                {{ $t('Delete') }}
                            </el-button>
                        </div>
                    </div>

                    <!-- 添加更多设备按钮 -->
                    <div class="flex justify-center mt-4">
                        <el-button
                            @click="addBatteryRow"
                            type="primary"
                            plain
                            round
                        >
                            {{ $t('add_device') }}
                        </el-button>
                    </div>
                </el-form>
            </div>
        </el-drawer>
        <el-drawer
            v-model="importVisible"
            :size="486"
            :show-close="false"
            @close="cancelImport"
            wrapClassName="drawerBox"
        >
            <template #header>
                <div
                    class="drawer-header flex items-center justify-between leading-5.5"
                >
                    <div
                        class="drawer-header text-primary-text dark:text-80-dark"
                    >
                        <span>{{ $t('batch_add_device') }}</span>
                    </div>
                    <div class="flex gap-x-3 items-center">
                        <el-button plain round @click="cancelImport">{{
                            $t('Cancle')
                        }}</el-button>
                        <el-button
                            plain
                            type="primary"
                            round
                            @click="confirmImport()"
                            >{{ $t('Confirm') }}</el-button
                        >
                    </div>
                </div>
            </template>
            <div class="text-primary-text dark:text-80-dark">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="mb-2">{{ $t('step_one') }}:</div>
                        <div>{{ $t('step_one_desc') }}</div>
                    </div>
                    <div>
                        <el-button @click="downloadTemp" round>{{
                            $t('download_template')
                        }}</el-button>
                    </div>
                </div>
                <div class="mt-5">
                    <div class="mb-2">{{ $t('step_two') }}</div>
                    <div class="relative">
                        <el-upload
                            :on-change="handleFileChange"
                            :auto-upload="false"
                            accept=".xlsx, .xls"
                            ref="uploadRef"
                            :limit="1"
                            drag
                            :show-file-list="false"
                            :on-exceed="handleExceed"
                            :on-remove="() => (selectedFile = null)"
                            v-if="!selectedFile"
                        >
                            <template #trigger>
                                <el-icon class="el-icon--upload w-20" size="60"
                                    ><upload-filled
                                /></el-icon>
                                <div class="el-upload__text pt-2">
                                    {{ $t('drag_upload_tip') }}
                                </div>
                            </template>
                            <template #file v-if="false">
                                <div v-if="false"></div>
                            </template>
                        </el-upload>
                        <div
                            class="flex items-center border border-border text-center justify-center cursor-pointer relative file-box rounded"
                            v-else
                            style="height: 138px"
                        >
                            <div class="file-text">
                                {{ selectedFile.name }}
                            </div>
                            <div
                                @click="() => (selectedFile = null)"
                                class="absolute w-full h-full left-0 right-0 flex justify-center items-center align-middle delete-mask rounded"
                                style="line-height: 138px"
                            >
                                <el-icon size="30">
                                    <Delete />
                                </el-icon>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </el-drawer>
        <el-drawer
            v-model="filterDrawerVisible"
            direction="rtl"
            :size="486"
            :show-close="false"
            @close="onCloseFilterDrawer"
            wrapClassName="drawerBox"
        >
            <template #header>
                <div
                    class="drawer-header flex items-center justify-between leading-5.5"
                >
                    <div
                        class="drawer-header text-primary-text dark:text-80-dark"
                    >
                        <span>{{ $t('filter') }}</span>
                    </div>
                    <div class="flex gap-x-3">
                        <el-button plain round @click="onCloseFilterDrawer">{{
                            $t('common_guanbi')
                        }}</el-button>
                        <el-button
                            plain
                            round
                            @click="onFilterReset"
                            type="primary"
                            :loading="addLoading"
                            >{{ $t('reset') }}</el-button
                        >
                        <el-button
                            plain
                            round
                            @click="onFilterDrawerSave"
                            type="primary"
                            :loading="addLoading"
                            >{{ $t('Confirm') }}</el-button
                        >
                    </div>
                </div>
            </template>
            <div>
                <div class="top">
                    <el-tabs
                        v-model="activeFilterName"
                        @tab-change="handleFilterTabChange"
                    >
                        <template #default>
                            <el-tab-pane
                                :label="$t('filter_by_project')"
                                name="project"
                            >
                                <div class="project-filter">
                                    <div class="filter-other">
                                        <DeviceFilterForm
                                            ref="deviceFilterFormRef"
                                            v-model:model="formState.model"
                                            v-model:cities="formState.cities"
                                            v-model:create-time-range="
                                                formState.createTimeRange
                                            "
                                            :device-options="deviceOptions"
                                            :city-options="cityOptions"
                                            :shortcuts="shortcuts"
                                            :loading="tableLoading"
                                            @filter-change="handleFilterChange"
                                            @filter-reset="handleFilterReset"
                                        />
                                    </div>
                                    <div
                                        class="mt-3 p-3 bg-background rounded-lg flex-1 h-0 flex flex-col"
                                    >
                                        <div class="project-search">
                                            <el-input
                                                v-model="projectKeywords"
                                                style="max-width: 600px"
                                                :placeholder="$t('placeholder')"
                                                class="input-with-select"
                                            >
                                                <template #append>
                                                    <el-button
                                                        @click="onSearchProject"
                                                    >
                                                        <el-icon>
                                                            <Search />
                                                        </el-icon>
                                                    </el-button>
                                                </template>
                                            </el-input>
                                        </div>
                                        <div class="pt-3">
                                            <div
                                                class="flex justify-start items-center"
                                            >
                                                <el-checkbox
                                                    v-model="checkAllFilter"
                                                    :indeterminate="
                                                        isIndeterminateFilter
                                                    "
                                                    @change="
                                                        handleCheckAllChangeFilter
                                                    "
                                                >
                                                    {{ $t('Select all') }}
                                                </el-checkbox>
                                                <el-button
                                                    size="small"
                                                    round
                                                    @click="changeView"
                                                    class="cursor-pointer flex items-center"
                                                >
                                                    <!-- <div class="w-7">
                                                    {{
                                                        isName
                                                            ? $t('Name')
                                                            : $t('No')
                                                    }}
                                                </div> -->
                                                    <!--  style="
                                                        transform: rotate(
                                                            90deg
                                                        );
                                                    " -->
                                                    <iconSvg
                                                        name="toggle"
                                                        class="icon-default w-3.5 h-3.5"
                                                    />
                                                </el-button>
                                            </div>
                                        </div>
                                        <div class="flex-1 h-0 overflow-auto">
                                            <el-checkbox-group
                                                v-model="checkedProject"
                                                @change="onChangeCheckedProject"
                                                class=""
                                            >
                                                <el-checkbox
                                                    v-for="item in projectList"
                                                    :value="item.id"
                                                    :key="item.id"
                                                    class="pl-4 relative"
                                                >
                                                    <div
                                                        class="absolute w-2.5 h-2.5 border-l border-b left-0 rounded-bl-sm opacity-60"
                                                    ></div>
                                                    {{
                                                        isName
                                                            ? item.projectName
                                                            : item.projectNo
                                                    }}
                                                    /
                                                    <span
                                                        >({{
                                                            item.batteryCount
                                                        }})</span
                                                    >
                                                </el-checkbox>
                                            </el-checkbox-group>
                                        </div>
                                    </div>
                                </div>
                            </el-tab-pane>
                            <el-tab-pane
                                :label="$t('filter_by_customer')"
                                name="customer"
                            >
                                <div class="project-filter">
                                    <div class="filter-other">
                                        <DeviceFilterForm
                                            ref="deviceFilterFormRef"
                                            v-model:model="formState.model"
                                            v-model:cities="formState.cities"
                                            v-model:create-time-range="
                                                formState.createTimeRange
                                            "
                                            :device-options="deviceOptions"
                                            :city-options="cityOptions"
                                            :shortcuts="shortcuts"
                                            :loading="tableLoading"
                                            @filter-change="handleFilterChange"
                                            @filter-reset="handleFilterReset"
                                        />
                                    </div>
                                    <div
                                        class="mt-3 p-3 bg-background rounded-lg flex-1 h-0 flex flex-col"
                                    >
                                        <div
                                            class="flex-1 h-0 overflow-y-auto pb-3 element-plus-tree"
                                        >
                                            <el-tree
                                                ref="customerTreeRef"
                                                :data="treeData"
                                                show-checkbox
                                                node-key="value"
                                                default-expand-all
                                                :props="{
                                                    label: 'label',
                                                    children: 'children',
                                                }"
                                                @check="handleCustomerCheck"
                                                check-strictly
                                            />
                                            <div
                                                class="flex-1"
                                                v-if="treeData?.length <= 0"
                                            >
                                                <el-empty></el-empty>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </el-tab-pane>
                        </template>
                    </el-tabs>
                </div>
            </div>
        </el-drawer>
        <!-- 设备绑定抽屉 -->
        <el-drawer
            v-model="bindDeviceVisible"
            :title="$t('Bind Single Device')"
            direction="rtl"
            :size="486"
            :show-close="false"
            @close="closeBindDrawer"
            wrapClassName="drawerBox"
        >
            <template #header>
                <div
                    class="drawer-header flex items-center justify-between leading-5.5"
                >
                    <div
                        class="drawer-header text-primary-text dark:text-80-dark"
                    >
                        <span>{{ $t('station_bangdingqiye') }}</span>
                    </div>
                    <div class="flex gap-x-3 items-center">
                        <el-button plain round @click="closeBindDrawer">{{
                            $t('common_guanbi')
                        }}</el-button>
                        <el-button
                            v-if="bindingMode === 'edit'"
                            plain
                            type="primary"
                            round
                            @click="saveBinding"
                            >{{ $t('Save') }}</el-button
                        >
                        <el-button
                            v-if="bindingMode === 'view'"
                            plain
                            type="primary"
                            round
                            @click="bindingMode = 'edit'"
                            >{{ $t('common_bianji') }}</el-button
                        >
                    </div>
                </div>
            </template>
            <div v-if="currentDevice">
                <el-form
                    :model="bindingForm"
                    label-position="left"
                    :label-width="itemLabelWidth"
                >
                    <el-form-item :label="$t('Device No')">
                        <el-input :value="currentDevice.sn" disabled />
                    </el-form-item>
                    <el-form-item :label="$t('station_bangdingqiye')">
                        <el-tree-select
                            v-model="bindingForm.companyId"
                            :data="treeData"
                            :placeholder="$t('placeholder_qingxuanze')"
                            class="w-full"
                            :disabled="bindingMode === 'view'"
                            :props="{
                                label: 'label',
                                value: 'value',
                                children: 'children',
                            }"
                            :render-after-expand="false"
                            check-strictly
                            :default-expand-all="false"
                            node-key="value"
                        />
                    </el-form-item>
                    <el-form-item :label="$t('Service Date')">
                        <el-date-picker
                            v-model="bindingForm.serviceExpireDate"
                            type="date"
                            :placeholder="$t('Please select service date')"
                            value-format="YYYY-MM-DD"
                            :disabled="bindingMode === 'view'"
                            :disabled-date="(date) => date < new Date()"
                            style="width: 100%"
                        />
                    </el-form-item>
                </el-form>
                <div class="w-5 h-5 absolute z-1000 right-0 bottom-0">
                    <div
                        class="w-5 h-5 absolute z-10"
                        @dblclick="showDiv = true"
                    ></div>
                    <confirm-button
                        :title="`Are you sure to unbind?`"
                        :content="content"
                        @confirm="unBindCustomer(currentDevice.id)"
                        @cancel="showDiv = false"
                        placement="bottom-end"
                        v-if="showDiv"
                    >
                        <template #reference>
                            <div
                                class="w-5 h-5 absolute z-20 right-0 bottom-0"
                            ></div>
                        </template>
                    </confirm-button>
                </div>
            </div>
        </el-drawer>

        <!-- 查看二维码抽屉 -->
        <el-drawer
            v-model="viewQRVisible"
            :title="$t('Device QR Code')"
            direction="rtl"
            :size="486"
            :show-close="false"
            @close="viewQRVisible = false"
            wrapClassName="drawerBox"
        >
            <template #header>
                <div
                    class="drawer-header flex items-center justify-between leading-5.5"
                >
                    <div
                        class="drawer-header text-primary-text dark:text-80-dark"
                    >
                        <span>{{ $t('Device QR Code') }}</span>
                    </div>
                    <div class="flex gap-x-3 items-center">
                        <el-button plain round @click="viewQRVisible = false">{{
                            $t('Cancle')
                        }}</el-button>
                        <el-button
                            plain
                            type="primary"
                            round
                            @click="downloadQRCode"
                            >{{ $t('Download') }}</el-button
                        >
                    </div>
                </div>
            </template>
            <div
                v-if="currentDevice"
                class="flex flex-col items-center qrcode-text"
            >
                <!-- <p class="mt-5 text-base">{{ $t('Device QR Code') }}</p> -->
                <p class="mt-2 mb-4 text-60 dark:text-60-dark">
                    {{ $t('Device No') }}: {{ currentDevice.sn }}
                </p>
                <img
                    style="width: 250px; height: 250px"
                    :src="qrCodeUrl"
                    alt=""
                    srcset=""
                />
            </div>
        </el-drawer>

        <!-- 服务续期抽屉 -->
        <el-drawer
            v-model="serviceRenewalVisible"
            direction="rtl"
            :size="486"
            :show-close="false"
            @close="closeServiceRenewalDrawer"
            wrapClassName="drawerBox"
        >
            <template #header>
                <div
                    class="drawer-header flex items-center justify-between leading-5.5"
                >
                    <div
                        class="drawer-header text-primary-text dark:text-80-dark"
                    >
                        <span>{{ $t('Service Renewal') }}</span>
                    </div>
                    <div class="flex gap-x-3 items-center">
                        <el-button
                            plain
                            round
                            @click="closeServiceRenewalDrawer"
                            >{{ $t('Cancle') }}</el-button
                        >
                        <el-button
                            plain
                            type="primary"
                            round
                            @click="saveServiceRenewal"
                            >{{ $t('Save') }}</el-button
                        >
                    </div>
                </div>
            </template>
            <div class="">
                <el-form
                    :model="serviceRenewalForm"
                    :label-width="labelWidth"
                    hide-required-asterisk
                >
                    <el-form-item :label="$t('Device No')">
                        <el-input
                            v-model="serviceRenewalForm.sn"
                            :placeholder="$t('Device No')"
                            disabled
                        />
                    </el-form-item>
                    <el-form-item :label="$t('Service Date')" required>
                        <el-date-picker
                            v-model="serviceRenewalForm.serviceExpireDate"
                            type="date"
                            :placeholder="$t('Please select service date')"
                            value-format="YYYY-MM-DD"
                            :disabled-date="
                                (date) => date < getMinSelectableDate()
                            "
                            style="width: 100%"
                        />
                    </el-form-item>
                </el-form>
            </div>
        </el-drawer>

        <!-- 批量设备绑定抽屉 -->
        <el-drawer
            v-model="batchBindVisible"
            direction="rtl"
            :size="486"
            :show-close="false"
            @close="closeBatchBindDrawer"
            wrapClassName="drawerBox"
        >
            <template #header>
                <div
                    class="drawer-header flex items-center justify-between leading-5.5"
                >
                    <div
                        class="drawer-header text-primary-text dark:text-80-dark"
                    >
                        <span>{{ $t('station_bangdingqiye') }}</span>
                    </div>
                    <div class="flex gap-x-3 items-center">
                        <el-button plain round @click="closeBatchBindDrawer">{{
                            $t('Cancle')
                        }}</el-button>
                        <el-button
                            plain
                            type="primary"
                            round
                            @click="confirmBatchBind"
                            >{{ $t('Confirm') }}</el-button
                        >
                    </div>
                </div>
            </template>
            <div class="h-full flex flex-col">
                <el-form
                    :model="batchBindForm"
                    label-position="left"
                    :label-width="itemLabelWidth"
                >
                    <el-form-item :label="$t('station_bangdingqiye')">
                        <el-tree-select
                            v-model="batchBindForm.companyId"
                            :data="treeData"
                            :placeholder="$t('placeholder_qingxuanze')"
                            class="w-full"
                            :props="{
                                label: 'label',
                                value: 'value',
                                children: 'children',
                            }"
                            :render-after-expand="false"
                            check-strictly
                            :default-expand-all="false"
                            node-key="value"
                        />
                    </el-form-item>
                    <el-form-item :label="$t('Service Date')">
                        <el-date-picker
                            v-model="batchBindForm.serviceExpireDate"
                            type="date"
                            :placeholder="$t('Please select service date')"
                            value-format="YYYY-MM-DD"
                            :disabled-date="(date) => date < new Date()"
                            style="width: 100%"
                        />
                    </el-form-item>
                </el-form>
                <div
                    class="flex-1 h-0 mt-4 border-t border-border dark:border-border-dark"
                >
                    <div
                        class="py-4 font-medium text-title dark:text-title-dark"
                    >
                        {{ $t('Choose Device') }}
                    </div>
                    <!-- 设备选择器将在这里实现 -->
                    <div class="p-3 bg-background rounded-lg device-selector">
                        <device-selector
                            ref="deviceSelectorRef"
                            v-model:selected-device-ids="
                                batchBindForm.deviceIds
                            "
                        />
                    </div>
                </div>
            </div>
        </el-drawer>

        <!-- 批量续期抽屉 -->
        <el-drawer
            v-model="batchRenewalVisible"
            direction="rtl"
            :size="486"
            :show-close="false"
            @close="closeBatchRenewalDrawer"
            wrapClassName="drawerBox"
        >
            <template #header>
                <div
                    class="drawer-header flex items-center justify-between leading-5.5"
                >
                    <div
                        class="drawer-header text-primary-text dark:text-80-dark"
                    >
                        <span>{{ $t('Service Renewal') }}</span>
                    </div>
                    <div class="flex gap-x-3 items-center">
                        <el-button
                            plain
                            round
                            @click="closeBatchRenewalDrawer"
                            >{{ $t('Cancle') }}</el-button
                        >
                        <el-button
                            plain
                            type="primary"
                            round
                            @click="confirmBatchRenewal"
                            >{{ $t('Confirm') }}</el-button
                        >
                    </div>
                </div>
            </template>
            <div class="h-full flex flex-col">
                <el-form
                    :model="batchRenewalForm"
                    label-position="left"
                    :label-width="itemLabelWidth"
                >
                    <el-form-item
                        :label="$t('Service Date')"
                        required
                        :labelW-width="labelWidth"
                    >
                        <el-date-picker
                            v-model="batchRenewalForm.serviceExpireDate"
                            type="date"
                            :placeholder="$t('Service Date')"
                            value-format="YYYY-MM-DD"
                            :disabled-date="(date) => date < new Date()"
                            style="width: 100%"
                        />
                    </el-form-item>
                </el-form>
                <div
                    class="flex-1 h-0 mt-4 border-t border-border dark:border-border-dark"
                >
                    <div
                        class="py-4 font-medium text-title dark:text-title-dark"
                    >
                        {{ $t('Choose Device') }}
                    </div>
                    <!-- 设备选择器 -->
                    <div class="p-3 bg-background rounded-lg device-selector">
                        <batch-renewal-selector
                            ref="batchRenewalSelectorRef"
                            v-model:selected-device-ids="
                                batchRenewalForm.deviceIds
                            "
                        />
                    </div>
                </div>
            </div>
        </el-drawer>

        <comfirm-pop
            :title="$t('确认删除该设备：')"
            :content="`${deleteRow?.sn}`"
            :visible="deleteDialogVisible"
            @confirm="confirmDeleteDevice"
            @update:visible="deleteDialogVisible = $event"
        />
    </div>
</template>

<script setup>
import {
    ref,
    computed,
    watch,
    reactive,
    toRaw,
    onMounted,
    h,
    nextTick,
} from 'vue'
import {
    CloseBold,
    Filter,
    Search,
    Delete,
    View,
    Link,
    Calendar,
} from '@element-plus/icons-vue'

import draggable from 'vuedraggable'
import powerApi from '@/apiService/power'
import apiService from '@/apiService/device'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useI18n } from 'vue-i18n'
const { t, locale } = useI18n()
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'
import { genFileId } from 'element-plus'
import { TableV2SortOrder } from 'element-plus'
import {
    formatterActiveStatus,
    getState,
    download,
    chargeStateP,
} from '@/common/util.js'
import dayjs from 'dayjs'
import debounce from 'lodash/debounce'
import iconSvg from '@/components/svgIcon'
import QRCode from '@/components/QRCode.vue'
import deviceSelector from '../components/deviceSelector.vue'
import batchRenewalSelector from '../components/batchRenewalSelector.vue'
import DeviceFilterForm from '@/components/DeviceFilterForm.vue'

const store = useStore()
const router = useRouter()
// Props定义
// 表格数据
const tableData = ref([])
const tableLoading = ref(false)
// 本地存储的 key
const COLUMN_SETTINGS_KEY = 'device_table_columns'

// 从本地存储读取列设置
const loadColumnSettings = () => {
    try {
        const savedColumns = localStorage.getItem(COLUMN_SETTINGS_KEY)
        if (savedColumns) {
            const parsedColumns = JSON.parse(savedColumns)
            // 验证存储的数据是否有效
            if (Array.isArray(parsedColumns) && parsedColumns.length > 0) {
                // 确保操作列始终存在
                if (!parsedColumns.includes('Operation')) {
                    parsedColumns.push('Operation')
                }
                selectedColumns.value = parsedColumns
                return true
            }
        }
    } catch (error) {
        console.error('Failed to load column settings:', error)
    }
    return false
}

// 保存列设置到本地存储
const saveColumnSettings = (columns) => {
    try {
        localStorage.setItem(COLUMN_SETTINGS_KEY, JSON.stringify(columns))
    } catch (error) {
        console.error('Failed to save column settings:', error)
    }
}

// 列配置相关
const columnList = ref([
    {
        key: 'sn',
        title: t('Device No'),
        width: '280px',
        fixed: 'left',
    },
    { key: 'hwid', title: 'HWID', width: '180px', align: 'left' },
    { key: 'imei', title: 'IMEI', width: '156px', align: 'left' },
    {
        key: 'vehicleType',
        title: t('Vehicle type'),
        width: '120px',
        align: 'left',
    },
    { key: 'model', title: t('Device model'), width: '164px', align: 'left' },
    {
        key: 'activeStatus',
        title: t('activation_status'),
        width: '144px',
        align: 'left',
    },
    {
        key: 'status',
        title: t('system_status'),
        width: '144px',
        align: 'left',
    },
    {
        key: 'serviceStatus',
        title: t('service_status'),
        width: '144px',
        align: 'left',
    },
    {
        key: 'serviceExpireDate',
        title: t('service_expire_time'),
        width: '144px',
        align: 'left',
    },
    {
        key: 'activeTime',
        title: t('activation_date'),
        width: '144px',
        align: 'left',
    },
    {
        key: 'signal4g',
        title: t('signal_strength'),
        width: '120px',
        align: 'left',
    },
    {
        key: 'lastHeartbeatTime',
        title: t('last_data_time'),
        width: '144px',
        align: 'left',
    },
    { key: 'soc', title: 'SOC', width: '80px', align: 'left' },
    { key: 'soh', title: 'SOH', width: '80px', align: 'left' },
    {
        key: 'alarmNub',
        title: t('alarm_count'),
        width: '120px',
        align: 'left',
    },
    {
        key: 'chgTimeSum',
        title: t('total_charge_time'),
        width: '144px',
        align: 'left',
    },
    {
        key: 'chgCapSum',
        title: t('total_charge_capacity'),
        width: '144px',
        align: 'left',
    },
    {
        key: 'dsgTimeSum',
        title: t('total_discharge_time'),
        width: '144px',
        align: 'left',
    },
    {
        key: 'dsgCapSum',
        title: t('total_discharge_capacity'),
        width: '144px',
        align: 'left',
    },
    {
        key: 'lastChargeTime',
        title: t('last_charge_time'),
        width: '144px',
        align: 'left',
    },
    {
        key: 'lastDischargeTime',
        title: t('last_discharge_time'),
        width: '144px',
        align: 'left',
    },
    {
        key: 'cycleCount',
        title: t('Cycle times'),
        width: '144px',
        align: 'left',
    },
    {
        key: 'customerName',
        title: t('station_tianjiazhandian'),
        width: '188px',
        align: 'left',
    },
    { key: 'Operation', title: t('Operation'), width: '88px', fixed: 'right' },
])
const checkedColumns = ref(columnList.value.map((col) => col.key))
const selectedColumns = ref([
    'sn',
    'hwid',
    'vehicleType',
    'model',
    'status',
    'signal4g',
    'lastHeartbeatTime',
    'address',
    'alarmNub',
    'chgCapSum',
    'lastDischargeTime',
    'cycleCount',
    'onlinePercent',
    'Operation',
])
const dialogVisible = ref(false)
const draggabledColumns = ref(columnList.value)
// 计算可见的列
function getResultArr(arr, rulesArr) {
    const map = arr.reduce((acc, item) => {
        acc[item.key] = item
        return acc
    }, {})
    return rulesArr.map((key) => map[key]).filter((item) => item !== undefined) // 过滤掉不存在的项
}
const visibleColumns = computed(() => {
    return getResultArr(columnList.value, selectedColumns.value)
})
const onChangeCheckedColumns = (e) => {
    // 确保操作列始终被选中
    if (!e.includes('Operation')) {
        e.push('Operation')
    }

    draggabledColumns.value = columnList.value.filter((item) => {
        return checkedColumns.value.includes(item.key)
    })
    if (e.length == columnList.value.length) {
        checkAll.value = true
        isIndeterminate.value = false
    } else if (e.length == 0) {
        checkAll.value = false
        isIndeterminate.value = false
    } else {
        isIndeterminate.value = true
    }
}

// 显示列选择器
const showColumnSelector = () => {
    checkedColumns.value = visibleColumns.value.map((col) => col.key)
    draggabledColumns.value = visibleColumns.value.map((col) => col)
    dialogVisible.value = true
}
// 定义排序状态枚举
const SortOrder = {
    NONE: undefined,
    ASC: 'asc',
    DESC: 'desc',
}

// 获取下一个排序状态
const getNextSortOrder = (currentOrder) => {
    // 如果当前状态是 undefined，则切换到 'asc'
    if (!currentOrder) {
        return SortOrder.ASC
    }
    // 如果当前状态是 'asc'，则切换到 'desc'
    if (currentOrder === SortOrder.ASC) {
        return SortOrder.DESC
    }
    // 如果当前状态是 'desc'，则切换到 undefined
    if (currentOrder === SortOrder.DESC) {
        return SortOrder.NONE
    }
    // 默认返回 undefined
    return SortOrder.NONE
}

// 处理排序变化
const sortBy = ref({
    sortField: undefined,
    sortOrder: undefined,
})

const handleSortChange = ({ column, prop, order }) => {
    // 构建排序参数
    sortBy.value = order
        ? {
              sortField: prop,
              sortOrder: order === 'ascending' ? 'asc' : 'desc',
          }
        : {
              sortField: undefined,
              sortOrder: undefined,
          }

    // 更新请求参数并重新获取数据
    debouncedGetDeviceData()
}

// 防抖版本的获取数据函数
const debouncedGetDeviceData = debounce(async () => {
    await getDeviceData()
}, 300)

// 修改所有可排序列的 headerCellRenderer，添加默认状态图标
const getSortIcon = (columnKey, sortState) => {
    const currentOrder = sortState[columnKey]
    // 默认状态（undefined）显示 sort 图标
    if (!currentOrder) {
        return h(iconSvg, {
            name: 'sort',
            class: 'w-3.5 h-3.5 opacity-40',
        })
    }
    // 升序状态显示 asc 图标
    if (currentOrder === SortOrder.ASC) {
        return h(iconSvg, {
            name: 'asc',
            class: 'w-3.5 h-3.5',
        })
    }
    // 降序状态显示 desc 图标
    return h(iconSvg, {
        name: 'desc',
        class: 'w-3.5 h-3.5',
    })
}

// 处理拖拽结束
const handleDragEnd = ({
    newDraggableIndex,
    newIndex,
    oldDraggableIndex,
    oldIndex,
}) => {
    // 可以在这里处理拖拽结束后的逻辑
}
const checkMove = (e) => {
    // 获取拖拽后的目标索引
    const targetIndex = e.draggedContext.futureIndex
    // 如果目标位置是前两个，则禁止拖拽
    return targetIndex >= 2
}
const closeDrawer = () => {
    dialogVisible.value = false
}

const onSave = () => {
    selectedColumns.value = draggabledColumns.value.map((col) => col.key)
    // 确保操作列始终在最后
    if (!selectedColumns.value.includes('Operation')) {
        selectedColumns.value.push('Operation')
    } else {
        // 如果操作列已存在，将其移到最后
        const operationIndex = selectedColumns.value.indexOf('Operation')
        if (operationIndex > -1) {
            selectedColumns.value.splice(operationIndex, 1)
            selectedColumns.value.push('Operation')
        }
    }
    ElMessage.success(t('Successed'))
    dialogVisible.value = false
    // 保存列设置到本地存储
    saveColumnSettings(selectedColumns.value)
}
const checkAll = ref(true)
const isIndeterminate = ref(true)
const handleCheckAllChange = (val) => {
    checkedColumns.value = val
        ? columnList.value.map((col) => col.key)
        : ['sn', 'hwid', 'Operation']
    draggabledColumns.value = columnList.value.filter((item) => {
        return checkedColumns.value.includes(item.key)
    })
    isIndeterminate.value = false
}
const onInvert = () => {
    const requiredColumns = ['sn', 'hwid']
    const currentChecked = [...checkedColumns.value]
    const allColumns = columnList.value.map((col) => col.key)

    // 反选逻辑：保留必选项，其他项取反
    checkedColumns.value = [
        ...requiredColumns,
        ...allColumns.filter(
            (key) =>
                !requiredColumns.includes(key) && !currentChecked.includes(key)
        ),
    ]

    // 更新拖拽列表
    draggabledColumns.value = columnList.value.filter((item) => {
        return checkedColumns.value.includes(item.key)
    })
}

// 重置到默认设置
const resetToDefault = () => {
    const defaultColumns = [
        'sn',
        'hwid',
        'vehicleType',
        'model',
        'status',
        'signal4g',
        'lastHeartbeatTime',
        'address',
        'alarmNub',
        'chgCapSum',
        'lastDischargeTime',
        'cycleCount',
        'onlinePercent',
        'Operation',
    ]

    selectedColumns.value = [...defaultColumns]
    checkedColumns.value = [...defaultColumns]
    draggabledColumns.value = columnList.value.filter((item) => {
        return defaultColumns.includes(item.key)
    })

    // 清除本地存储
    localStorage.removeItem(COLUMN_SETTINGS_KEY)

    ElMessage.success(t('Successed'))
}

// 添加设备
const dialogFormVisible = ref(false)
const formRef = ref(null)
const formData = reactive({
    projectNo: '',
    batteryList: [
        {
            batteryNo: '',
            sn: '',
        },
    ],
})

// 项目列表数据
const projectPageInfo = ref({
    current: 1,
    size: 1000,
})
const projectData = ref([])
const getProjectData = async () => {
    let params = {
        ...projectPageInfo.value,
    }
    let res = await powerApi.getProjectPageList(params)
    projectData.value = res.data.data.records
}

// 添加设备
const addDevice = async () => {
    if (projectData.value.length) {
        //
    } else {
        await getProjectData()
    }
    dialogFormVisible.value = true
}
const cancelAdd = () => {
    dialogFormVisible.value = false
    formRef.value.clearValidate()
    formRef.value.resetFields()
    formData.batteryList = [
        {
            batteryNo: '',
            sn: '',
        },
    ]
}
// 添加电池行
const addBatteryRow = () => {
    formData.batteryList.push({
        batteryNo: '',
        sn: '',
    })
}
//复制行
const copyBatteryRow = (item, index) => {
    formData.batteryList.splice(index + 1, 0, {
        batteryNo: formData.batteryList[index].batteryNo,
        sn: formData.batteryList[index].sn,
    })
}

// 删除电池行
const removeBatteryRow = (index) => {
    formData.batteryList.splice(index, 1)
}

const hasUniqueIds = (arr, type) => {
    const seenIds = new Set()
    for (const item of arr) {
        if (seenIds.has(item[type])) {
            return false
        }
        seenIds.add(item[type])
    }
    return true
}
// 提交表单
const addLoading = ref(false)
const confirmAdd = async () => {
    if (!formRef.value) return
    // 检查设备编号是否重复
    let hasRepeatSn = hasUniqueIds(formData.batteryList, 'sn')
    let hasRepeatBatteryNo = hasUniqueIds(formData.batteryList, 'batteryNo')
    if (formData.batteryList.some((item) => !item.batteryNo)) {
        addLoading.value = false
        ElMessage.error(t('tianjiashebei_tips10'))
        return
    } else if (!hasRepeatBatteryNo) {
        addLoading.value = false
        ElMessage.error(t('tianjiashebei_tips11'))
        return
    } else if (formData.batteryList.some((item) => !item.sn)) {
        addLoading.value = false
        ElMessage.error(t('tianjiashebei_tips02'))
        return
    } else if (!hasRepeatSn) {
        addLoading.value = false
        ElMessage.error(t('tianjiashebei_tips03'))
        return
    }

    formRef.value.validate().then(async () => {
        let params = {
            ...toRaw(formData),
        }

        await powerApi.batchAddBattery(params)
        // return
        // 提交成功后关闭抽屉
        // 刷新数据
        ElMessage.success(t('Successed'))
        await getDeviceData()
        cancelAdd()
    })
}

const emit = defineEmits(['search'])
const searchValueD = ref('')

const importVisible = ref(false)
const addDeviceImport = () => {
    //
    importVisible.value = true
}
const downloadTemp = () => {
    //
    window.open(
        'https://battery-monitor.oss-cn-shanghai.aliyuncs.com/frontend/excelTemplete/%E5%AF%BC%E5%85%A5%E8%AE%BE%E5%A4%87%E6%A8%A1%E6%9D%BF.xlsx',
        '_blank'
    )
}
const cancelImport = () => {
    //
    importVisible.value = false
}

const selectedFile = ref(null)

const handleFileChange = (file) => {
    // 文件类型验证
    const allowedTypes = [
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ]
    const isExcel = allowedTypes.includes(file.raw.type)
    const isLt5M = file.raw.size / 1024 / 1024 < 5

    if (!isExcel) {
        ElMessage.error(t('Only Excel files can be uploaded'))
        return
    }
    if (!isLt5M) {
        ElMessage.error(t('upload_tips001') + '5MB!')
        return
    }
    selectedFile.value = file.raw
}
const uploadRef = ref()
const handleExceed = (files) => {
    uploadRef.value?.clearFiles()
    const file = files[0]
    file.uid = genFileId()
    uploadRef.value?.handleStart(file)
}
const confirmImport = async () => {
    if (!selectedFile.value) {
        ElMessage.warning(t('Please upload the file first'))
        return
    }
    try {
        const formData = new FormData()
        formData.append('file', selectedFile.value)
        await powerApi.batchImportBattery(formData)
        ElMessage.success(t('Successed'))
        importVisible.value = false
        selectedFile.value = undefined
        await getDeviceData()
        // 可以在这里添加刷新列表的逻辑
    } catch (error) {
        // ElMessage.error(t('Password_tips09'), error)
    }
}
const rowClick = (rowData) => {
    router.push({
        name: 'equipmentDetail',
        query: {
            sn: rowData.sn,
            projectId: rowData.projectId,
        },
    })
}
const filterDrawerVisible = ref(false)
const onCloseFilterDrawer = () => {
    //
    filterDrawerVisible.value = false
}
const projectList = ref()
const getProjectList = async (flag) => {
    let params = {
        keyword: projectKeywords.value,
    }
    let res = await powerApi.getProjectAndBatteryCounts(params)
    projectList.value = res.data.data
    if (!flag) {
        checkedProject.value = []
    }
}
const convertToTree = (data) => {
    return data.provinces.map((province) => {
        return {
            label: province.province,
            value: province.province,
            children: province.cities.map((city) => {
                return {
                    label: city.city,
                    value: city.city,
                    children: [], // 目前 city 层没有更深的子节点
                }
            }),
        }
    })
}

const cityOptions = ref([])
const hasCities = ref(false)
const getReginDeviceTree = async () => {
    //
    let res = await powerApi.getReginDeviceTree()
    hasCities.value = true
    cityOptions.value =
        res.data.data && res.data.data.provinces
            ? convertToTree(res.data.data)
            : []
}
const openFilter = async () => {
    getReginDeviceTree()
    filterDrawerVisible.value = true
}
const drawerObject = ref()
const onFilterDrawerSave = async () => {
    drawerObject.value = {
        projectIds: checkedProject.value,
        customerIds: checkedCustomers.value,
        model: formState.model || undefined,
        city: formState.cities,
        activeStartDate: formState.createTimeRange
            ? formState.createTimeRange[0]
            : undefined,
        activeEndDate: formState.createTimeRange
            ? formState.createTimeRange[1]
            : undefined,
    }
    filterFlag.value = true
    await getDeviceData(true)
    filterDrawerVisible.value = false
}

const onFilterReset = async () => {
    filterFlag.value = false
    await getProjectList()
    formState.cities = void 0
    formState.model = void 0
    formState.createTimeRange = []
    checkedCustomers.value = []
    customerTreeRef.value?.setCheckedKeys([], false)
}
const activeFilterName = ref('project')
const handleFilterTabChange = async (name) => {
    if (
        name === 'customer' &&
        (!treeData.value || treeData.value.length === 0)
    ) {
        await getTreeData()
    }
}
const projectKeywords = ref('')
const checkedProject = ref()
const onChangeCheckedProject = (e) => {
    //
    if (e.length == projectList.value.length) {
        checkAllFilter.value = true
        isIndeterminateFilter.value = false
    } else if (e.length == 0) {
        checkAllFilter.value = false
        isIndeterminateFilter.value = false
    } else {
        isIndeterminateFilter.value = true
    }
}
const checkAllFilter = ref(false)
const isIndeterminateFilter = ref(false)

const handleCheckAllChangeFilter = (val) => {
    checkedProject.value = val ? projectList.value.map((col) => col.id) : []
    isIndeterminateFilter.value = false
}
const onSearchProject = () => {
    getProjectList(true)
}
const devicePageInfo = ref({
    current: 1,
    size: 10,
})
const devicePageTotal = ref(0)
const filterFlag = ref(false)
const getDeviceData = async () => {
    try {
        tableLoading.value = true
        const { sortField, sortOrder } = sortBy.value
        let params
        if (filterFlag.value) {
            params = {
                status: statusFilter.value || undefined,
                keyword: searchValueD.value || undefined,
                model: modelFilter.value || undefined,
                vehicleType: vehicleTypeFilter.value || undefined,
                activeStatus: activeStatusFilter.value,
                serviceStatus: serviceStatusFilter.value || undefined,
                ...devicePageInfo.value,
                ...drawerObject.value,
                sortField,
                sortOrder,
            }
        } else {
            params = {
                status: statusFilter.value || undefined,
                keyword: searchValueD.value || undefined,
                model: modelFilter.value || undefined,
                vehicleType: vehicleTypeFilter.value || undefined,
                activeStatus: activeStatusFilter.value,
                serviceStatus: serviceStatusFilter.value || undefined,
                ...devicePageInfo.value,
                sortField,
                sortOrder,
            }
        }
        let res = await powerApi.getDevicePageList(params)
        tableData.value = res.data.data.records
        devicePageTotal.value = res.data.data.total
        tableLoading.value = false
    } catch (error) {
        tableLoading.value = false
        console.error('获取设备数据失败:', error)
    }
}
const devicePageChange = async () => {
    await getDeviceData(false, undefined)
}

// 防抖版本的分页切换函数
const debouncedDevicePageChange = debounce(async () => {
    await getDeviceData()
}, 300)

const handleDeviceSizeChange = async (e) => {
    devicePageInfo.value.size = e
    debouncedDevicePageChange()
}

const onSearchD = async () => {
    await getDeviceData(false, undefined)
}

// 防抖版本的搜索函数
const debouncedOnSearchD = debounce(async () => {
    await getDeviceData()
}, 300)
onMounted(async () => {
    // 先加载列设置，再获取数据
    const hasLoadedSettings = loadColumnSettings()
    await store.dispatch('dictionary/getDictionary', 'vehicleType')
    await store.dispatch('dictionary/getDictionary', 'powerBmsModel')
    await getDeviceData()
    await getProjectList()
    // 初始化时获取企业树形数据
    await getTreeData()
    // 如果没有加载到存储的设置，使用默认设置
    if (!hasLoadedSettings) {
        console.log('Using default column settings')
    }
})
const customKeywords = ref('')
const onSearchCustom = () => {
    //
}
const formatterActiveTime = (e) => {
    return e.activeTime ? dayjs(e.activeTime).format('YYYY-MM-DD') : ''
}
const vehicleTypes = computed(
    () => store.state.dictionary.dictionaries.vehicleType || []
)
const getVehicleType = (val) => {
    if (vehicleTypes.value.length == 0) return
    if (!val) return '-'
    return vehicleTypes.value.find((item) => item.value == val).label
}

const formatterStatus = (e) => {
    if (e.status != undefined && e.status == null) return '-'
    if (e.status == 3 && e.lastHeartbeatTime) {
        let end = dayjs(new Date())
        let start = dayjs(e.lastHeartbeatTime)
        const preciseHoursDiff = (end.diff(start) / (1000 * 60 * 60)).toFixed(2)
        return (
            t(getState(e.status, 'power').label) + '(' + preciseHoursDiff + 'h)'
        )
    } else {
        return t(getState(e.status, 'power').label)
    }
    // 计算精确的小时差（含小数）
}
const formatterServiceStatus = (e) => {
    return e == 1 ? t('Normal') : t('Expired')
}

const deviceOptions = computed(
    () => store.state.dictionary.dictionaries.powerBmsModel || []
)

const labelWidth = computed(() => {
    let res =
        locale.value == 'zh' ? '70px' : locale.value == 'en' ? '120px' : '120px'
    return res
})
const itemLabelWidth = computed(() => {
    let res =
        locale.value == 'zh' ? '84px' : locale.value == 'en' ? '140px' : '140px'
    return res
})
const rules = ref()
const formState = reactive({
    model: [],
    cities: [],
    createTimeRange: [undefined, undefined],
})

// 设备筛选表单引用
const deviceFilterFormRef = ref(null)

// 筛选处理函数
const handleFilterChange = (filterData) => {
    // 数据已经在formState中，直接重新获取数据
    debouncedGetDeviceData()
}

const handleFilterReset = () => {
    // 重置已经在组件内部处理，直接重新获取数据
    debouncedGetDeviceData()
}

const shortcuts = ref([
    {
        text: t('Last Week'),
        value: () => {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            return [start, end]
        },
    },
    {
        text: t('Last 30 Days'),
        value: () => {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            return [start, end]
        },
    },
    {
        text: t('Last Year'),
        value: () => {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 366)
            return [start, end]
        },
    },
])
const isName = ref(false)
const changeView = () => {
    isName.value = !isName.value
}
const onDeleteItem = (item) => {
    const index = draggabledColumns.value.findIndex(
        (col) => col.key === item.key
    )
    if (index < 2) {
        return
    }
    if (index > -1) {
        draggabledColumns.value.splice(index, 1)
        const checkedIndex = checkedColumns.value.indexOf(item.key)
        if (checkedIndex > -1) {
            checkedColumns.value.splice(checkedIndex, 1)
        }
    }
}

// 在 script setup 部分添加新的状态变量
const modelFilter = ref('')
const selectedModels = ref([])
const vehicleTypeFilter = ref([])
const selectedVehicleTypes = ref([])
const activeStatusFilter = ref(null)
const selectedActiveStatus = ref(null)
const statusFilter = ref([])
const selectedStatus = ref([])
const serviceStatusFilter = ref(null)
const selectedServiceStatus = ref(null)

// 筛选弹窗显示状态
const vehicleTypeFilterVisible = ref(false)
const modelFilterVisible = ref(false)
const activeStatusFilterVisible = ref(false)
const statusFilterVisible = ref(false)
const serviceStatusFilterVisible = ref(false)

// 筛选处理函数
const onVehicleTypeFilterChange = (value, checked) => {
    if (checked) {
        if (!selectedVehicleTypes.value.includes(value)) {
            selectedVehicleTypes.value.push(value)
        }
    } else {
        const index = selectedVehicleTypes.value.indexOf(value)
        if (index > -1) {
            selectedVehicleTypes.value.splice(index, 1)
        }
    }
}

const onVehicleTypeFilterReset = () => {
    selectedVehicleTypes.value = []
    vehicleTypeFilter.value = []
    debouncedGetDeviceData()
    vehicleTypeFilterVisible.value = false
}

const onVehicleTypeFilterConfirm = () => {
    vehicleTypeFilter.value = selectedVehicleTypes.value
    debouncedGetDeviceData()
    vehicleTypeFilterVisible.value = false
}

const onModelFilterChange = (value, checked) => {
    if (checked) {
        if (!selectedModels.value.includes(value)) {
            selectedModels.value.push(value)
        }
    } else {
        const index = selectedModels.value.indexOf(value)
        if (index > -1) {
            selectedModels.value.splice(index, 1)
        }
    }
}

const onModelFilterReset = () => {
    selectedModels.value = []
    modelFilter.value = []
    debouncedGetDeviceData()
    modelFilterVisible.value = false
}

const onModelFilterConfirm = () => {
    modelFilter.value = selectedModels.value
    debouncedGetDeviceData()
    modelFilterVisible.value = false
}

const onActiveStatusFilterChange = (value) => {
    selectedActiveStatus.value = value
}

const onActiveStatusFilterReset = () => {
    selectedActiveStatus.value = null
    activeStatusFilter.value = null
    debouncedGetDeviceData()
    activeStatusFilterVisible.value = false
}

const onActiveStatusFilterConfirm = () => {
    activeStatusFilter.value = selectedActiveStatus.value
    debouncedGetDeviceData()
    activeStatusFilterVisible.value = false
}

const onStatusFilterChange = (value, checked) => {
    if (checked) {
        if (!selectedStatus.value.includes(value)) {
            selectedStatus.value.push(value)
        }
    } else {
        const index = selectedStatus.value.indexOf(value)
        if (index > -1) {
            selectedStatus.value.splice(index, 1)
        }
    }
}

const onStatusFilterReset = () => {
    selectedStatus.value = []
    statusFilter.value = []
    debouncedGetDeviceData()
    statusFilterVisible.value = false
}

const onStatusFilterConfirm = () => {
    statusFilter.value = selectedStatus.value
    debouncedGetDeviceData()
    statusFilterVisible.value = false
}

const onServiceStatusFilterChange = (value) => {
    selectedServiceStatus.value = value
}

const onServiceStatusFilterReset = () => {
    selectedServiceStatus.value = null
    serviceStatusFilter.value = null
    debouncedGetDeviceData()
    serviceStatusFilterVisible.value = false
}

const onServiceStatusFilterConfirm = () => {
    serviceStatusFilter.value = selectedServiceStatus.value
    debouncedGetDeviceData()
    serviceStatusFilterVisible.value = false
}

const sortState = ref({
    activeTime: SortOrder.NONE,
    signal4g: SortOrder.NONE,
    lastHeartbeatTime: SortOrder.NONE,
    soc: SortOrder.NONE,
    soh: SortOrder.NONE,
    alarmNub: SortOrder.NONE,
    chgTimeSum: SortOrder.NONE,
    chgCapSum: SortOrder.NONE,
    dsgTimeSum: SortOrder.NONE,
    dsgCapSum: SortOrder.NONE,
    lastChargeTime: SortOrder.NONE,
    lastDischargeTime: SortOrder.NONE,
    cycleCount: SortOrder.NONE,
    serviceExpireDate: SortOrder.NONE,
})

// --- Operation Column Logic ---
/**
 * @description: 设备绑定抽屉的可见性
 */
const bindDeviceVisible = ref(false)
/**
 * @description: 查看二维码抽屉的可见性
 */
const viewQRVisible = ref(false)
/**
 * @description: 服务续期抽屉的可见性
 */
const serviceRenewalVisible = ref(false)
/**
 * @description: 当前操作的设备数据
 */
const currentDevice = ref(null)
/**
 * @description: 绑定抽屉的模式 ('view' or 'edit')
 */
const bindingMode = ref('view') // 'view' or 'edit'
/**
 * @description: 企业列表，用于绑定设备
 */
const companyList = ref([])
/**
 * @description: 绑定表单的数据模型
 */
const bindingForm = reactive({
    companyId: '',
    serviceExpireDate: '',
})
/**
 * @description: 服务续期表单的数据模型
 */
const serviceRenewalForm = reactive({
    sn: '',
    serviceExpireDate: '',
})
/**
 * @description: Vue QR组件的引用
 */
const qrCodeRef = ref(null)

/**
 * @description: 计算当前设备的二维码URL
 */
const qrCodeUrl = computed(() => {
    // return 'https://ming-enterprise.oss-cn-hangzhou.aliyuncs.com/power-battery/qrcode/sn12345678.png?Expires=4904249194&OSSAccessKeyId=LTAI5t8cF26nCFFt1vesUoFx&Signature=PtBALTPp0aO98dg8Z3%2FB%2Bf0uP04%3D'
    if (currentDevice.value && currentDevice.value.qrCodeUrl) {
        return currentDevice.value.qrCodeUrl
    } else {
        return ''
    }
})

/**
 * @description: 获取企业树结构数据
 */
const fetchCompanyList = async () => {
    try {
        await getTreeData()
        // 将树形数据转换为扁平列表（如果需要保持原有逻辑兼容）
        companyList.value = flattenTreeData(treeData.value)
    } catch (error) {
        // ElMessage.error('获取企业列表失败')
    }
}

/**
 * @description: 将树形数据转换为扁平结构
 */
const flattenTreeData = (treeData) => {
    const result = []
    const traverse = (nodes) => {
        nodes.forEach((node) => {
            result.push({
                id: node.id,
                name: node.name,
                label: node.name,
                value: node.id,
            })
            if (node.children && node.children.length > 0) {
                traverse(node.children)
            }
        })
    }
    traverse(treeData)
    return result
}

/**
 * @description: 处理操作列下拉菜单的命令
 * @param {string} command - 命令标识
 * @param {object} rowData - 当前行数据
 */
const deleteDialogVisible = ref(false)
const deleteRow = ref()
const handleOperation = async (command, rowData) => {
    currentDevice.value = rowData
    switch (command) {
        case 'bindDevice':
            await fetchCompanyList()
            bindingForm.companyId = rowData.customerId
            bindingForm.serviceExpireDate = rowData.serviceExpireDate
            bindingMode.value = rowData.customerId ? 'view' : 'edit'
            bindDeviceVisible.value = true
            break
        case 'viewQRCode':
            viewQRVisible.value = true
            break
        case 'serviceRenewal':
            serviceRenewalForm.sn = rowData.sn
            serviceRenewalForm.serviceExpireDate = ''
            serviceRenewalVisible.value = true
            break
        case 'deleteDevice':
            deleteRow.value = rowData
            deleteDialogVisible.value = true
        // ElMessageBox.confirm(
        //     `${t('确认删除该设备：')}${rowData.sn}`,
        //     t('确认删除'),
        //     {
        //         confirmButtonText: t('Confirm'),
        //         cancelButtonText: t('Cancle'),
        //         type: 'warning',
        //     }
        // )
        //     .then(async () => {
        //         try {
        //             let res = await powerApi.delBms({ id: rowData.id })
        //             if (res.data.data) {
        //                 ElMessage.success(
        //                     `${t('设备:')}${rowData.sn}${t('已删除')}`
        //                 )
        //             }
        //             await getDeviceData()
        //         } catch (error) {
        //             ElMessage.error('删除失败')
        //         }
        //     })
        //     .catch(() => {})
        // break
    }
}
const confirmDeleteDevice = async () => {
    try {
        // 调用删除API
        let res = await powerApi.delBms({ id: deleteRow.value.id })
        if (res.data.data) {
            ElMessage.success(t('Successed'))
            // 刷新数据
            await getDeviceData()
        }
    } catch (error) {
        // ElMessage.error(t('Failed, please try again later'))
    }
}

/**
 * @description: 关闭设备绑定抽屉并重置状态
 */
const closeBindDrawer = () => {
    bindDeviceVisible.value = false
    currentDevice.value = null
    showDiv.value = false
}

/**
 * @description: 💥 保存设备绑定信息（包含测试用逻辑）
 */
const saveBinding = async () => {
    if (!bindingForm.companyId) {
        return ElMessage.warning('请选择要绑定的企业')
    }
    try {
        // await powerApi.bindDevice({
        //     deviceId: currentDevice.value.id,
        //     customerId: bindingForm.companyId,
        // })
        const params = {
            bmsIds: [currentDevice.value.id],
            customerId: bindingForm.companyId,
            serviceExpireDate: bindingForm.serviceExpireDate,
        }

        // TODO: 调用批量绑定API
        let res = await powerApi.batchBindCustomer(params)
        if (res.data.code == 0 && res.data.data) {
            ElMessage.success(`成功绑定 ${params.bmsIds.length} 台设备`)
        }
        closeBindDrawer()
        await getDeviceData()
    } catch (error) {
        // ElMessage.error('绑定失败')
    }
}

/**
 * @description: 下载二维码图片
 */

const downloadQRCode = () => {
    window.open(qrCodeUrl.value, '_blank')
}

const test = () => {}

const batchBindVisible = ref(false)
const deviceSelectorRef = ref(null)
const batchBindForm = reactive({
    companyId: '',
    serviceExpireDate: '',
    deviceIds: [],
})

// 批量续期相关变量
const batchRenewalVisible = ref(false)
const batchRenewalSelectorRef = ref(null)
const batchRenewalForm = reactive({
    serviceExpireDate: '',
    deviceIds: [],
})

const openBatchBindDrawer = async () => {
    // 清空表单数据
    batchBindForm.companyId = ''
    batchBindForm.serviceExpireDate = ''
    batchBindForm.deviceIds = []

    await fetchCompanyList()
    batchBindVisible.value = true

    // 等待下一个 tick，确保组件已经渲染
    await nextTick()

    // 重置设备选择器的筛选条件
    if (deviceSelectorRef.value) {
        deviceSelectorRef.value.resetFilters()
    }
}

const closeBatchBindDrawer = () => {
    batchBindVisible.value = false
    batchBindForm.companyId = ''
    batchBindForm.serviceExpireDate = ''
    batchBindForm.deviceIds = []
}

const confirmBatchBind = async () => {
    // 验证是否选择了企业
    if (!batchBindForm.companyId) {
        ElMessage.warning(t('请选择要绑定的企业'))
        return
    }

    // 验证是否选择了设备
    if (!batchBindForm.deviceIds || batchBindForm.deviceIds.length === 0) {
        ElMessage.warning(t('请选择要绑定的设备'))
        return
    }

    try {
        // 构造API所需的数据格式
        const params = {
            bmsIds: batchBindForm.deviceIds,
            customerId: batchBindForm.companyId,
            serviceExpireDate: batchBindForm.serviceExpireDate,
        }

        // TODO: 调用批量绑定API
        let res = await powerApi.batchBindCustomer(params)
        if (res.data.code == 0 && res.data.data) {
            ElMessage.success(`成功绑定 ${params.bmsIds.length} 台设备`)
        }

        // 关闭抽屉并重置表单
        closeBatchBindDrawer()

        // 刷新设备列表
        await getDeviceData()
    } catch (error) {
        console.error('批量绑定失败:', error)
        // ElMessage.error('批量绑定失败，请重试')
    }
}
/**
 * @description: 企业树形数据
 */
const treeData = ref([])

/**
 * @description: 获取企业树形结构数据
 */
const getTreeData = async () => {
    const getCompanyInfo = computed(() => store.getters['user/getUserInfoData'])
    try {
        const {
            data: { data, code },
        } = await apiService.getDeviceTree({
            supplierId: getCompanyInfo?.value?.orgId,
            businessType: 'vehicle_battery',
        })
        if (code === 0) {
            // 递归处理节点，确保每个节点都有label和value属性
            const processTreeNodes = (nodes) => {
                if (!nodes || !Array.isArray(nodes)) return []
                return nodes.map((node) => ({
                    ...node,
                    label: node.name || node.label,
                    value: node.id || node.value,
                    children:
                        node.children && node.children.length > 0
                            ? processTreeNodes(node.children)
                            : undefined,
                }))
            }

            const tree = [
                {
                    name: getCompanyInfo?.value?.orgName,
                    id: getCompanyInfo?.value?.orgId,
                    label: getCompanyInfo?.value?.orgName,
                    value: getCompanyInfo?.value?.orgId,
                    children: processTreeNodes(data || []),
                },
            ]

            treeData.value = tree
        }
    } catch (error) {
        console.error('获取企业树形数据失败:', error)
        // ElMessage.error('获取企业数据失败')
        // 设置默认数据以避免错误
        treeData.value = [
            {
                name: '默认企业',
                id: 'default',
                label: '默认企业',
                value: 'default',
                children: [],
            },
        ]
    }
}
const isOperator = computed(() => {
    return store.state.user.userInfoData.roles.includes('operation_staff')
    // return false
})
const exportExcel = (data, headers, fileName) => {
    // 此处当返回json文件时需要先对data进行JSON.stringify处理，其他类型文件不用做处理
    const blob = new Blob([data], {
        type: headers['content-type'],
    })
    let dom = document.createElement('a')
    let url = window.URL.createObjectURL(blob)
    dom.href = url
    dom.download = decodeURI(fileName)
    dom.style.display = 'none'
    document.body.appendChild(dom)
    dom.click()
    dom.parentNode.removeChild(dom)
    window.URL.revokeObjectURL(url)
}
// 批量下载二维码的loading状态
const downloadQRLoading = ref(false)

const BatchDownloadQRCode = async () => {
    // if (downloadQRLoading.value) return // 防止重复点击

    downloadQRLoading.value = true
    try {
        const bmsIds = tableData.value.map((item) => {
            return item.id
        })
        let res = await powerApi.batchDownloadQrCode({
            bmsIds,
        })
        const { data, headers } = res
        const fileName = 'QRcodes.zip'
        exportExcel(data, headers, fileName)
        ElMessage.success(t('下载成功'))
    } catch (error) {
        console.error('批量下载二维码失败:', error)
        // ElMessage.error(t('下载失败，请重试'))
    } finally {
        setTimeout(() => {
            downloadQRLoading.value = false
        }, 1500)
    }
}

const customerTreeRef = ref(null)
const checkedCustomers = ref([])

const handleCustomerCheck = (data, { checkedKeys }) => {
    checkedCustomers.value = checkedKeys
}
const showDiv = ref(false)
const unBindCustomer = async (id) => {
    let res = await powerApi.unBindCustomer({ id })
    if (res.data.data) {
        ElMessage.success('Success!')
    } else {
        // ElMessage.error('Error!')
    }
}
/**
 * @description: 关闭服务续期抽屉
 */
const closeServiceRenewalDrawer = () => {
    serviceRenewalVisible.value = false
    serviceRenewalForm.sn = ''
    serviceRenewalForm.serviceExpireDate = ''
}

/**
 * @description: 保存服务续期
 */
const saveServiceRenewal = async () => {
    if (!serviceRenewalForm.serviceExpireDate) {
        ElMessage.warning(t('Please select service date'))
        return
    }

    try {
        const params = {
            bmsIds: [currentDevice.value.id],
            serviceExpireDate: serviceRenewalForm.serviceExpireDate,
        }

        const res = await powerApi.batchUpdateServiceExpireDate(params)
        if (res.data.data) {
            ElMessage.success(t('Successed'))
            // 刷新数据
            await getDeviceData()
            // 关闭弹窗
            closeServiceRenewalDrawer()
        }
    } catch (error) {
        ElMessage.error(t('Failed, please try again later'))
    }
}

/**
 * @description: 获取最小可选日期（当前设备的serviceExpireDate之后）
 */
const getMinSelectableDate = () => {
    if (currentDevice.value && currentDevice.value.serviceExpireDate) {
        const expireDate = new Date(currentDevice.value.serviceExpireDate)
        expireDate.setDate(expireDate.getDate()) // 只能选择过期日期之后的日期
        return expireDate
    }
    return new Date() // 如果没有过期日期，则从今天开始
}

const batchRenewal = async () => {
    // 清空表单数据
    batchRenewalForm.serviceExpireDate = ''
    batchRenewalForm.deviceIds = []

    batchRenewalVisible.value = true

    // 等待下一个 tick，确保组件已经渲染
    await nextTick()

    // 重置设备选择器的筛选条件
    if (batchRenewalSelectorRef.value) {
        batchRenewalSelectorRef.value.resetFilters()
    }
}

/**
 * @description: 关闭批量续期抽屉
 */
const closeBatchRenewalDrawer = () => {
    batchRenewalVisible.value = false
    batchRenewalForm.serviceExpireDate = ''
    batchRenewalForm.deviceIds = []
}

/**
 * @description: 确认批量续期
 */
const confirmBatchRenewal = async () => {
    if (!batchRenewalForm.serviceExpireDate) {
        ElMessage.warning(t('Please select service date'))
        return
    }

    if (batchRenewalForm.deviceIds.length === 0) {
        ElMessage.warning(t('Please select devices'))
        return
    }

    try {
        const params = {
            bmsIds: batchRenewalForm.deviceIds,
            serviceExpireDate: batchRenewalForm.serviceExpireDate,
        }

        const res = await powerApi.batchUpdateServiceExpireDate(params)
        if (res.data.data) {
            ElMessage.success(t('Successed'))
            // 刷新数据
            await getDeviceData()
            // 关闭弹窗
            closeBatchRenewalDrawer()
        }
    } catch (error) {
        ElMessage.error(t('Failed, please try again later'))
    }
}
</script>

<style scoped lang="less">
.custom-table {
    width: 100%;
    // height: 100%;
    // height: 714px;
    height: calc(~'100vh - 240px');
    position: relative;
    .table-header {
        position: absolute;
        width: 100%;
        left: 0;
        top: -32px;
    }
}
.table-box {
    // height: 670px;
}

.column-selector {
    max-height: calc(100vh - 108px);
    overflow-y: auto;
}
.column-draggable {
    max-height: calc(100vh - 76px);
    overflow-y: auto;
}
.column-item {
    display: flex;
    align-items: center;
    padding: 8px;
    border-bottom: 1px solid #eee;
}

.drag-handle {
    cursor: move;
    margin-right: 8px;
    color: #909399;
    width: 24px;
    height: 24px;
    font-size: 24px;
}
.drag-handle-disabled {
    cursor: not-allowed;
    margin-right: 8px;
    color: #909399;
    width: 24px;
    height: 24px;
    font-size: 24px;
    opacity: 0.4;
}

:deep(.el-checkbox) {
    margin-right: 0;
    width: 100%;
}

:deep(.el-checkbox.is-disabled) {
    cursor: not-allowed;
}
:deep(.el-form-item) {
    margin-bottom: 12px;
}
.project-filter {
    height: calc(100vh - 148px);
    display: flex;
    flex-direction: column;
}
.filter-other {
    height: 128px;
    :deep(.el-form-item) {
        margin-bottom: 12px;
        &:last-child {
            margin-bottom: 0;
        }
    }
}
:deep(.el-upload-dragger) {
    padding: 20px 10px;
    .el-icon--upload {
        margin-bottom: 8px;
    }
}
:deep(.el-upload-dragger) {
    background-color: rgba(255, 255, 255, 0.2);
}
.el-upload__text {
    color: var(--themeColor);
}

.delete-mask {
    z-index: 99;
    background-color: rgba(255, 255, 255, 0.01);
    backdrop-filter: blur(10px);
    display: none;
}
.file-box {
    &:hover {
        .file-text {
            opacity: 0.2;
        }
        .delete-mask {
            display: block;
        }
    }
}
:deep(.el-dropdown) {
    .el-button-group > .el-button:first-child {
        background-color: transparent !important;
        // color: var(--themeColor) !important;
        border-top-left-radius: 999px;
        border-bottom-left-radius: 999px;
        &:hover {
            background-color: var(--themeColor) !important;
            color: #fff !important;
        }
    }
    .el-button-group .el-button:last-child {
        background-color: transparent !important;
        // color: var(--themeColor) !important;
        border-top-right-radius: 999px;
        border-bottom-right-radius: 999px;
        &:hover {
            background-color: var(--themeColor) !important;
            color: #fff !important;
        }
    }
    .el-dropdown__caret-button {
        width: 40px;
        padding-right: 4px;
    }
}
:deep(.hasFilter) {
    color: var(--themeColor);
}
:deep(.el-button:focus-visible) {
    outline: none !important;
}
.dark {
    :deep(.el-tree-node__content) {
        &:hover {
            background-color: rgba(255, 255, 255, 0.1) !important;
        }
    }
    :deep(.el-tree-node:focus > .el-tree-node__content) {
        background-color: rgba(255, 255, 255, 0.1) !important;
    }
}
:deep(.el-tree-node__content .el-checkbox) {
    margin-right: 8px;
    width: auto;
}
// .qrcode-text {
//     color: var(--themeColor);
// }
// .element-plus-tree {
//     :deep(.el-tree) {
//         .el-tree-node {
//             border-left: 1px solid var(--border);
//         }
//     }
// }
:deep(.el-dropdown-menu__item) {
    justify-content: flex-end;
}
.device-selector {
    height: calc(100% - 52px);
}
</style>
